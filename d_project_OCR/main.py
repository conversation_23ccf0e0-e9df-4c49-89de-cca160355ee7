import os
import time
import torch
import fitz  # PyMuPDF
import customtkinter as ctk
from tkinter import filedialog, messagebox
from PIL import Image, ImageTk
from transformers import <PERSON>Tokenizer, AutoProcessor, AutoModelForImageTextToText
import pyperclip
import threading


# ==============================
# OCR Engine Class
# ==============================
class DocumentOCR:
    def __init__(self, model_path: str, use_cuda: bool = True):
        self.device = torch.device("cuda" if use_cuda and torch.cuda.is_available() else "cpu")
        print(f"🔧 Using device: {self.device}")

        self.model = AutoModelForImageTextToText.from_pretrained(
            model_path,
            torch_dtype=torch.float16 if self.device.type == "cuda" else torch.float32,
            device_map=None
        ).to(self.device)
        self.model.eval()

        self.tokenizer = AutoTokenizer.from_pretrained(model_path)
        self.processor = AutoProcessor.from_pretrained(model_path)

    def extract_document_text_smart(self, image: Image.Image, page_num: int = 0) -> str:
        try:
            start_time = time.time()
            print(f"\n[INFO] OCR processing page {page_num + 1}...")

            prompt = (
                "Extract all visible text from this CMD terminal screenshot exactly as it appears. "
                "Preserve indentation, line breaks, symbols, and formatting. Do not summarize. "
                "Return only the raw plain text output. Do not add extra notes or explanations."
            )

            messages = [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": [
                    {"type": "image", "image": image},
                    {"type": "text", "text": prompt},
                ]},
            ]

            text = self.processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
            inputs = self.processor(text=[text], images=[image], padding=True, return_tensors="pt").to(
                self.model.device)

            output_ids = self.model.generate(**inputs, max_new_tokens=15000, do_sample=False)
            generated_ids = [output_ids[len(input_ids):] for input_ids, output_ids in zip(inputs.input_ids, output_ids)]
            output_text = self.processor.batch_decode(generated_ids, skip_special_tokens=True,
                                                      clean_up_tokenization_spaces=True)

            execution_time = time.time() - start_time
            print(f"[INFO] ✅ OCR completed in {execution_time:.2f} seconds")

            return output_text[0]
        except Exception as e:
            print(f"❌ Error in OCR (page {page_num + 1}): {e}")
            return ""

    @staticmethod
    def pdf_to_images(pdf_path, output_dir="temp_images"):
        os.makedirs(output_dir, exist_ok=True)
        doc = fitz.open(pdf_path)
        image_paths = []
        for i, page in enumerate(doc):
            pix = page.get_pixmap()
            img_path = os.path.join(output_dir, f"page_{i + 1}.png")
            pix.save(img_path)
            image_paths.append(img_path)
        return image_paths

    def extract_text_from_file(self, file_path: str):
        all_text = ""
        if file_path.lower().endswith(".pdf"):
            image_paths = self.pdf_to_images(file_path)
            for idx, img_path in enumerate(image_paths):
                img = Image.open(img_path).convert("RGB")
                text = self.extract_document_text_smart(img, page_num=idx)
                all_text += f"\n\n--- Page {idx + 1} ---\n\n{text}"
        else:
            img = Image.open(file_path).convert("RGB")
            all_text = self.extract_document_text_smart(img, page_num=0)
        return all_text


# ==============================
# CustomTkinter GUI
# ==============================
class OCRApp(ctk.CTk):
    def __init__(self, ocr_engine: DocumentOCR):
        super().__init__()
        self.ocr_engine = ocr_engine
        self.title("📄 D_OCR ")
        self.geometry("1200x700")
        self.resizable(False, False)

        # Variables for image handling
        self.image = None
        self.img_tk = None
        self.zoom_factor = 1.0
        self.file_path = None

        # Variables for dragging
        self.drag_start_x = 0
        self.drag_start_y = 0
        self.image_x = 0
        self.image_y = 0
        self.is_dragging = False
        self.canvas_image_id = None

        # Configure grid → 60/40 split
        self.grid_columnconfigure(0, weight=3, uniform="group1")
        self.grid_columnconfigure(1, weight=2, uniform="group1")
        self.grid_rowconfigure(0, weight=1)

        self.setup_ui()

        # Bind canvas events after a short delay to ensure canvas is ready
        self.after(200, self.setup_canvas_events)

        # Show initial message
        self.after(300, self.show_initial_message)

    def setup_ui(self):
        # ================= Left panel (60%) =================
        self.left_frame = ctk.CTkFrame(self, corner_radius=10)
        self.left_frame.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)

        # Top button row
        self.btn_frame = ctk.CTkFrame(self.left_frame)
        self.btn_frame.pack(fill="x", pady=5, padx=5)

        # Configure grid columns for buttons
        for i in range(5):
            self.btn_frame.grid_columnconfigure(i, weight=1)

        self.load_btn = ctk.CTkButton(self.btn_frame, text="📂 Load", command=self.load_file, width=80)
        self.load_btn.grid(row=0, column=0, padx=2, pady=5, sticky="ew")

        self.refresh_btn = ctk.CTkButton(self.btn_frame, text="🔄 Clear", command=self.refresh_all, width=80)
        self.refresh_btn.grid(row=0, column=1, padx=2, pady=5, sticky="ew")

        self.zoom_in_btn = ctk.CTkButton(self.btn_frame, text="➕ In", command=self.zoom_in, width=60)
        self.zoom_in_btn.grid(row=0, column=2, padx=2, pady=5, sticky="ew")

        self.zoom_out_btn = ctk.CTkButton(self.btn_frame, text="➖ Out", command=self.zoom_out, width=60)
        self.zoom_out_btn.grid(row=0, column=3, padx=2, pady=5, sticky="ew")

        self.reset_view_btn = ctk.CTkButton(self.btn_frame, text="🎯 Reset", command=self.reset_view, width=70)
        self.reset_view_btn.grid(row=0, column=4, padx=2, pady=5, sticky="ew")

        # Canvas for image display
        self.canvas = ctk.CTkCanvas(self.left_frame, bg="#2b2b2b", highlightthickness=0, width=600, height=500)
        self.canvas.pack(fill="both", expand=True, padx=5, pady=5)

        # ================= Right panel (40%) =================
        self.right_frame = ctk.CTkFrame(self, corner_radius=10)
        self.right_frame.grid(row=0, column=1, sticky="nsew", padx=10, pady=10)

        self.copy_btn = ctk.CTkButton(self.right_frame, text="📋 Copy Result", command=self.copy_result)
        self.copy_btn.pack(pady=5)

        self.result_box = ctk.CTkTextbox(self.right_frame, wrap="word", font=("Consolas", 11))
        self.result_box.pack(fill="both", expand=True, padx=10, pady=10)

    def show_initial_message(self):
        """Show initial message on empty canvas"""
        if not self.image:
            self.canvas.delete("all")
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            if canvas_width > 1 and canvas_height > 1:  # Canvas is ready
                self.canvas.create_text(
                    canvas_width // 2,
                    canvas_height // 2,
                    text="📂 Load an image or PDF to begin",
                    fill="#a0a0a0",
                    font=("Arial", 14)
                )

    def setup_canvas_events(self):
        """Setup canvas mouse events for dragging"""
        self.canvas.bind("<Button-1>", self.on_drag_start)
        self.canvas.bind("<B1-Motion>", self.on_drag_motion)
        self.canvas.bind("<ButtonRelease-1>", self.on_drag_end)
        self.canvas.bind("<Enter>", self.on_canvas_enter)
        self.canvas.bind("<Leave>", self.on_canvas_leave)

    def on_canvas_enter(self, event):
        """Change cursor to hand when hovering over canvas if image is zoomed"""
        if self.image and self.zoom_factor > 1.0:
            self.canvas.configure(cursor="hand2")

    def on_canvas_leave(self, event):
        """Reset cursor when leaving canvas"""
        self.canvas.configure(cursor="")

    def on_drag_start(self, event):
        """Start dragging the image"""
        if self.image and self.zoom_factor > 1.0:
            self.is_dragging = True
            self.drag_start_x = event.x
            self.drag_start_y = event.y
            self.canvas.configure(cursor="fleur")  # Move cursor

    def on_drag_motion(self, event):
        """Handle image dragging"""
        if self.is_dragging and self.image:
            # Calculate movement delta
            dx = event.x - self.drag_start_x
            dy = event.y - self.drag_start_y

            # Update image position
            self.image_x += dx
            self.image_y += dy

            # Update drag start position for next motion
            self.drag_start_x = event.x
            self.drag_start_y = event.y

            # Redraw image at new position
            self.update_image_position()

    def on_drag_end(self, event):
        """End dragging"""
        if self.is_dragging:
            self.is_dragging = False
            if self.zoom_factor > 1.0:
                self.canvas.configure(cursor="hand2")
            else:
                self.canvas.configure(cursor="")

    def update_image_position(self):
        """Update image position on canvas"""
        if self.canvas_image_id and self.img_tk:
            # Get canvas dimensions
            self.canvas.update()
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            # Fallback dimensions
            if canvas_width <= 1 or canvas_height <= 1:
                canvas_width = 600
                canvas_height = 500

            # Calculate final position (center + offset)
            final_x = canvas_width // 2 + self.image_x
            final_y = canvas_height // 2 + self.image_y

            # Move the image
            self.canvas.coords(self.canvas_image_id, final_x, final_y)

    def load_file(self):
        """Load and process file"""
        file_path = filedialog.askopenfilename(
            title="Select File",
            filetypes=(("Image/PDF files", "*.png *.jpg *.jpeg *.pdf"), ("All files", "*.*"))
        )
        if file_path:
            self.file_path = file_path
            self.reset_view()

            # Clear previous results
            self.result_box.delete("1.0", "end")
            self.result_box.insert("end", "[INFO] Loading file...\n")
            self.update_idletasks()

            try:
                if not file_path.lower().endswith(".pdf"):
                    # Load and display image immediately
                    self.image = Image.open(file_path).convert("RGB")
                    print(f"[DEBUG] Image loaded: {self.image.size}")

                    # Force canvas update and display image
                    self.after(50, self.force_display_image)
                else:
                    self.clear_canvas()
                    # Show PDF loaded message
                    self.canvas.update_idletasks()
                    canvas_width = self.canvas.winfo_width() or 600
                    canvas_height = self.canvas.winfo_height() or 500
                    self.canvas.create_text(
                        canvas_width // 2,
                        canvas_height // 2,
                        text="📑 PDF Loaded\nProcessing...",
                        fill="white",
                        font=("Arial", 16),
                        justify="center"
                    )

                # Show processing message
                self.result_box.delete("1.0", "end")
                self.result_box.insert("end", "[INFO] Running OCR... Please wait.\n")
                self.update_idletasks()

                # Disable buttons during processing
                self.set_buttons_state(False)

                # Run OCR in separate thread to prevent UI freezing
                thread = threading.Thread(target=self.process_ocr, args=(file_path,))
                thread.daemon = True
                thread.start()

            except Exception as e:
                print(f"[ERROR] Failed to load file: {e}")
                self.result_box.delete("1.0", "end")
                self.result_box.insert("end", f"❌ Error loading file: {str(e)}")
                self.set_buttons_state(True)

    def process_ocr(self, file_path):
        """Process OCR in separate thread"""
        try:
            result = self.ocr_engine.extract_text_from_file(file_path)
            # Update UI in main thread
            self.after(0, self.update_ocr_result, result)
        except Exception as e:
            error_msg = f"❌ Error during OCR processing: {str(e)}"
            self.after(0, self.update_ocr_result, error_msg)

    def update_ocr_result(self, result):
        """Update OCR result in main thread"""
        self.result_box.delete("1.0", "end")
        self.result_box.insert("end", result)
        # Re-enable buttons
        self.set_buttons_state(True)

    def set_buttons_state(self, enabled):
        """Enable/disable buttons"""
        state = "normal" if enabled else "disabled"
        self.load_btn.configure(state=state)
        self.refresh_btn.configure(state=state)
        self.zoom_in_btn.configure(state=state)
        self.zoom_out_btn.configure(state=state)
        self.reset_view_btn.configure(state=state)

    def clear_canvas(self):
        """Clear canvas and reset image variables"""
        self.canvas.delete("all")
        self.canvas_image_id = None
        self.img_tk = None

    def force_display_image(self):
        """Force display image with proper canvas dimensions"""
        if self.image:
            print(f"[DEBUG] Forcing image display...")
            # Make sure canvas is properly sized
            self.canvas.update()
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            print(f"[DEBUG] Canvas dimensions: {canvas_width}x{canvas_height}")

            if canvas_width <= 1 or canvas_height <= 1:
                # Canvas not ready, try again
                self.after(50, self.force_display_image)
                return

            self.display_image()

    def display_image(self):
        """Display image on canvas"""
        if not self.image:
            print("[DEBUG] No image to display")
            return

        try:
            print(f"[DEBUG] Displaying image, zoom: {self.zoom_factor}")

            # Calculate new size based on zoom
            w, h = self.image.size
            new_size = (int(w * self.zoom_factor), int(h * self.zoom_factor))
            print(f"[DEBUG] Original size: {w}x{h}, New size: {new_size}")

            img_resized = self.image.resize(new_size, Image.LANCZOS)
            self.img_tk = ImageTk.PhotoImage(img_resized)

            # Clear canvas
            self.clear_canvas()

            # Get canvas dimensions
            self.canvas.update()
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()

            # Fallback if canvas dimensions are not ready
            if canvas_width <= 1 or canvas_height <= 1:
                canvas_width = 600
                canvas_height = 500

            print(f"[DEBUG] Canvas size: {canvas_width}x{canvas_height}")

            # Calculate position
            final_x = canvas_width // 2 + self.image_x
            final_y = canvas_height // 2 + self.image_y

            print(f"[DEBUG] Image position: {final_x}, {final_y}")

            # Create image on canvas
            self.canvas_image_id = self.canvas.create_image(
                final_x, final_y,
                image=self.img_tk,
                anchor="center"
            )

            print(f"[DEBUG] Image created with ID: {self.canvas_image_id}")

            # Update cursor based on zoom level
            if self.zoom_factor > 1.0:
                self.canvas.configure(cursor="hand2")
            else:
                self.canvas.configure(cursor="")

        except Exception as e:
            print(f"[ERROR] Error displaying image: {e}")
            import traceback
            traceback.print_exc()

    def zoom_in(self):
        """Zoom in the image"""
        if self.image:
            self.zoom_factor *= 1.2
            print(f"[DEBUG] Zoom in to: {self.zoom_factor}")
            self.display_image()

    def zoom_out(self):
        """Zoom out the image"""
        if self.image:
            self.zoom_factor = max(0.1, self.zoom_factor / 1.2)
            print(f"[DEBUG] Zoom out to: {self.zoom_factor}")
            self.display_image()

    def reset_view(self):
        """Reset zoom and position"""
        print("[DEBUG] Resetting view")
        self.zoom_factor = 1.0
        self.image_x = 0
        self.image_y = 0
        if self.image:
            self.display_image()

    def refresh_all(self):
        """Refresh and clear everything"""
        # Clear image and canvas
        self.image = None
        self.file_path = None
        self.clear_canvas()

        # Reset view parameters
        self.reset_view()

        # Clear result box
        self.result_box.delete("1.0", "end")

        # Reset cursor
        self.canvas.configure(cursor="")

        # Show empty state message
        self.canvas.update_idletasks()
        canvas_width = self.canvas.winfo_width() or 600
        canvas_height = self.canvas.winfo_height() or 500
        self.canvas.create_text(
            canvas_width // 2,
            canvas_height // 2,
            text="📂 Load an image or PDF to begin",
            fill="#a0a0a0",
            font=("Arial", 14)
        )

    def copy_result(self):
        """Copy OCR result to clipboard"""
        text = self.result_box.get("1.0", "end").strip()
        if text and text != "[INFO] Running OCR... Please wait.":
            pyperclip.copy(text)
            messagebox.showinfo("Copied", "OCR result copied to clipboard!")
        else:
            messagebox.showwarning("Warning", "No text to copy.")


# ==============================
# Run App
# ==============================
if __name__ == "__main__":
    model_path = "./model/d_ocr_model/snapshots/3baad182cc87c65a1861f0c30357d3467e978172"
    ocr_engine = DocumentOCR(model_path)

    app = OCRApp(ocr_engine)
    app.mainloop()